# MixVPR 项目配置日志

## 项目概述
- **项目**: MixVPR - Visual Place Recognition
- **目标**: 配置并运行 MixVPR 项目
- **工作目录**: `/root/autodl-tmp`
- **数据盘**: `/root/autodl-tmp` (50GB)

## 已完成的操作

### 1. 环境检查 ✅
- **时间**: 初始阶段
- **操作**: 检查工作目录结构
- **结果**: 发现 MixVPR-main 项目文件夹

### 2. 数据集下载脚本创建 ✅
- **时间**: 初始阶段
- **文件**: `download_dataset.py`
- **功能**: 下载 GSV Cities 数据集到数据盘
- **配置**: 
  - 下载位置: `/root/autodl-tmp/kagglehub_cache`
  - 数据集: `amaralibey/gsv-cities`

### 3. 依赖问题解决 ✅
- **问题**: `pip install cv2` 失败
- **原因**: 包名错误，应该是 `opencv-python`
- **解决**: 说明正确的安装方法

### 4. 数据集路径配置 ✅
- **文件**: `MixVPR-main/dataloaders/GSVCitiesDataset.py`
- **问题**: 原始代码硬编码路径，导致 `FileNotFoundError`
- **修改内容**:
  - **原始代码**:
    ```python
    BASE_PATH = '../datasets/gsv_cities/'
    if not Path(BASE_PATH).exists():
        raise FileNotFoundError('BASE_PATH is hardcoded, please adjust to point to gsv_cities')
    ```
  - **修改后**:
    ```python
    BASE_PATH = os.environ.get('GSV_CITIES_PATH', '../datasets/gsv_cities/')
    if not Path(BASE_PATH).exists():
        print(f"GSV Cities dataset not found at: {BASE_PATH}")
        print("Please either:")
        print("1. Set GSV_CITIES_PATH environment variable to point to your dataset")
        print("2. Download the dataset first")
        print("3. Or modify BASE_PATH in this file")
        raise FileNotFoundError(f'GSV Cities dataset not found at {BASE_PATH}. Please download the dataset first or set GSV_CITIES_PATH environment variable.')
    ```
- **改进**:
  - 支持环境变量 `GSV_CITIES_PATH` 动态配置路径
  - 提供更详细的错误提示和解决方案

### 5. 预训练模型管理 ✅
- **下载**: ResNet50 4096维模型
- **源文件**: `/root/autodl-tmp/resnet50_MixVPR_4096_channels(1024)_rows(4).ckpt`
- **操作**:
  ```bash
  mkdir -p /root/autodl-tmp/MixVPR-main/LOGS
  mv /root/autodl-tmp/resnet50_MixVPR_4096_channels(1024)_rows(4).ckpt /root/autodl-tmp/MixVPR-main/LOGS/
  ```

### 6. 模型加载测试脚本创建 ✅
- **文件**: `test_model_loading.py`
- **功能**: 快速验证环境配置和预训练权重
- **测试内容**:
  - 包导入测试 (PyTorch, TorchVision, PyTorch Lightning, TIMM, OpenCV)
  - GPU环境检测
  - 模型实例化
  - 预训练权重加载
  - 模型推理测试 (使用随机输入)
- **运行方法**:
  ```bash
  # 在 torch_env 环境中运行
  cd /root/autodl-tmp
  python test_model_loading.py
  ```

## 当前状态

### ✅ 已完成
- [x] 项目文件结构检查
- [x] 数据集下载脚本创建
- [x] 数据集路径配置修改
- [x] 预训练模型下载和放置
- [x] LOGS 目录创建

### 🔄 进行中
- [ ] GSV Cities 数据集下载 (用户正在执行)

### ❌ 待完成
- [ ] 安装项目依赖: `pip install -r MixVPR-main/requirements.txt`
- [ ] 安装额外依赖: `pip install opencv-python`
- [ ] 配置数据集环境变量
- [ ] GPU 环境检查
- [ ] 项目运行测试

## 下一步计划

1. **等待数据集下载完成**
2. **安装所有依赖包**
3. **配置数据集路径**
4. **运行项目测试**

## 文件结构
```
/root/autodl-tmp/
├── MixVPR-main/
│   ├── LOGS/
│   │   └── resnet50_MixVPR_4096_channels(1024)_rows(4).ckpt
│   ├── requirements.txt
│   ├── main.py
│   ├── demo.py
│   └── dataloaders/
│       └── GSVCitiesDataset.py (已修改)
├── download_dataset.py
├── kagglehub_cache/ (数据集下载中)
└── log.md (本文件)
```

## 备注
- 数据盘空间充足: 50GB 可用
- 使用 torch_env 环境
- 项目支持 GPU 加速
