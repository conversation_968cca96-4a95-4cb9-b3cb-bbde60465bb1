#!/usr/bin/env python3
"""
MixVPR 简化模型测试脚本
只测试模型加载和推理，不涉及数据集
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# 添加项目路径到 Python path
sys.path.append('/root/autodl-tmp/MixVPR-main')

def test_imports():
    """测试必要的包导入"""
    print("🔍 测试包导入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        import torchvision
        print(f"✅ TorchVision: {torchvision.__version__}")
        
        import pytorch_lightning as pl
        print(f"✅ PyTorch Lightning: {pl.__version__}")
        
        import timm
        print(f"✅ TIMM: {timm.__version__}")
        
        try:
            import cv2
            print(f"✅ OpenCV: {cv2.__version__}")
        except ImportError:
            print("⚠️  OpenCV 未安装 (可选)")
            
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_gpu():
    """测试GPU可用性"""
    print("\n🔍 测试GPU环境...")
    
    if torch.cuda.is_available():
        print(f"✅ CUDA 可用: {torch.cuda.get_device_name(0)}")
        print(f"✅ CUDA 版本: {torch.version.cuda}")
        print(f"✅ GPU 数量: {torch.cuda.device_count()}")
        return True
    else:
        print("⚠️  CUDA 不可用，将使用CPU")
        return False

def test_model_components():
    """测试模型组件"""
    print("\n🔍 测试模型组件...")
    
    try:
        # 测试 backbone
        from models.backbones.resnet import ResNet
        backbone = ResNet('resnet50', pretrained=True, layers_to_freeze=2, layers_to_crop=[4])
        print("✅ ResNet backbone 创建成功")
        
        # 测试 aggregator
        from models.aggregators.mixvpr import MixVPR
        aggregator = MixVPR(
            in_channels=1024,
            in_h=20,
            in_w=20,
            out_channels=1024,
            mix_depth=4,
            mlp_ratio=1,
            out_rows=4
        )
        print("✅ MixVPR aggregator 创建成功")
        
        return backbone, aggregator
        
    except Exception as e:
        print(f"❌ 模型组件测试失败: {e}")
        return None, None

def test_model_inference():
    """测试完整模型推理"""
    print("\n🔍 测试完整模型推理...")
    
    try:
        # 手动创建模型（避免数据集依赖）
        backbone, aggregator = test_model_components()
        if backbone is None or aggregator is None:
            return False
            
        # 创建完整模型
        class SimpleVPRModel(torch.nn.Module):
            def __init__(self, backbone, aggregator):
                super().__init__()
                self.backbone = backbone
                self.aggregator = aggregator
                
            def forward(self, x):
                x = self.backbone(x)
                x = self.aggregator(x)
                return x
        
        model = SimpleVPRModel(backbone, aggregator)
        print("✅ 完整模型创建成功")
        
        # 检查预训练权重文件
        ckpt_path = '/root/autodl-tmp/MixVPR-main/LOGS/resnet50_MixVPR_4096_channels(1024)_rows(4).ckpt'
        if not Path(ckpt_path).exists():
            print(f"❌ 预训练权重文件不存在: {ckpt_path}")
            return False
            
        print(f"✅ 找到预训练权重: {ckpt_path}")
        
        # 加载预训练权重
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        state_dict = torch.load(ckpt_path, map_location=device)
        
        # 过滤掉不匹配的键
        model_dict = model.state_dict()
        filtered_dict = {}
        for k, v in state_dict.items():
            if k in model_dict and model_dict[k].shape == v.shape:
                filtered_dict[k] = v
            else:
                print(f"⚠️  跳过不匹配的权重: {k}")
        
        model.load_state_dict(filtered_dict, strict=False)
        print("✅ 成功加载预训练权重")
        
        # 设置为评估模式
        model.eval()
        print("✅ 模型设置为评估模式")
        
        # 测试推理
        batch_size = 2
        input_tensor = torch.randn(batch_size, 3, 320, 320)
        
        if device == 'cuda':
            model = model.cuda()
            input_tensor = input_tensor.cuda()
            
        print(f"✅ 输入张量形状: {input_tensor.shape}")
        
        # 前向推理
        with torch.no_grad():
            output = model(input_tensor)
            
        print(f"✅ 输出张量形状: {output.shape}")
        print(f"✅ 输出维度: {output.shape[1]} (应该是 4096)")
        
        # 验证输出是否已归一化
        norms = torch.norm(output, dim=1)
        print(f"✅ 输出向量范数: {norms.cpu().numpy()}")
        
        if torch.allclose(norms, torch.ones_like(norms), atol=1e-6):
            print("✅ 输出向量已正确归一化")
        else:
            print("⚠️  输出向量可能未正确归一化")
            
        return True
        
    except Exception as e:
        print(f"❌ 模型推理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 MixVPR 简化模型测试")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("包导入", test_imports),
        ("GPU环境", test_gpu),
        ("模型推理", test_model_inference),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！MixVPR 模型可以正常工作！")
        print("💡 模型已准备就绪，可以用于:")
        print("   - 特征提取")
        print("   - 图像检索")
        print("   - 视觉位置识别")
    else:
        print("\n⚠️  部分测试失败，请检查配置")
        
    return all_passed

if __name__ == "__main__":
    main()
