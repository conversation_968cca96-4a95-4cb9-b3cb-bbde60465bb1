#!/usr/bin/env python3
"""
下载 GSV Cities 数据集到数据盘
"""
import os
import kagglehub

def download_gsv_cities_dataset():
    """下载 GSV Cities 数据集到数据盘"""
    
    # 设置下载位置到数据盘
    cache_dir = '/root/autodl-tmp/kagglehub_cache'
    os.environ['KAGGLEHUB_CACHE'] = cache_dir
    
    # 创建缓存目录（如果不存在）
    os.makedirs(cache_dir, exist_ok=True)
    
    print(f"设置下载位置: {cache_dir}")
    print("开始下载 GSV Cities 数据集...")
    
    try:
        # 下载数据集
        path = kagglehub.dataset_download("amaralibey/gsv-cities")
        print(f"数据集下载完成!")
        print(f"数据集路径: {path}")
        
        # 显示下载的文件
        if os.path.exists(path):
            print(f"\n数据集内容:")
            for root, dirs, files in os.walk(path):
                level = root.replace(path, '').count(os.sep)
                indent = ' ' * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 1)
                for file in files[:10]:  # 只显示前10个文件
                    print(f"{subindent}{file}")
                if len(files) > 10:
                    print(f"{subindent}... 还有 {len(files) - 10} 个文件")
        
        return path
        
    except Exception as e:
        print(f"下载失败: {e}")
        return None

if __name__ == "__main__":
    dataset_path = download_gsv_cities_dataset()
    if dataset_path:
        print(f"\n✅ 数据集已成功下载到: {dataset_path}")
    else:
        print("\n❌ 数据集下载失败")
